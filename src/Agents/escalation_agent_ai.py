"""
Escalation Agent AI - Autonomous Escalation Management
=====================================================

This module implements an autonomous AI agent for intelligent escalation management.
The agent can independently detect when tickets need escalation, determine appropriate
escalation paths, manage escalation workflows, and coordinate with other agents.

Key Autonomous Capabilities:
- Independent escalation criteria evaluation
- Autonomous escalation path determination
- Dynamic escalation rule adaptation
- Proactive escalation prevention
- Learning from escalation outcomes
- Collaborative coordination with other agents
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple
import json
import uuid

from .base_agent import BaseAgent, AgentState, AgentCapability, AgentGoal
from .agent_communication import MessageType, MessagePriority

logger = logging.getLogger(__name__)


class EscalationAgentAI(BaseAgent):
    """
    Autonomous AI Agent for Escalation Management
    
    This agent exhibits autonomous behavior by:
    - Continuously monitoring tickets for escalation criteria
    - Independently determining when escalation is needed
    - Autonomously selecting appropriate escalation paths
    - Adapting escalation rules based on outcomes
    - Learning from escalation patterns and success rates
    - Collaborating with other agents for optimal outcomes
    """
    
    def __init__(self, escalation_config: Optional[Dict[str, Any]] = None, agent_id: Optional[str] = None):
        """
        Initialize the Escalation Agent AI.
        
        Args:
            escalation_config: Configuration for escalation parameters
            agent_id: Optional custom agent ID
        """
        agent_id = agent_id or f"escalation_agent_{uuid.uuid4().hex[:8]}"
        
        # Define agent capabilities
        capabilities = [
            AgentCapability.ESCALATION,
            AgentCapability.DECISION_MAKING,
            AgentCapability.LEARNING,
            AgentCapability.COMMUNICATION,
            AgentCapability.PLANNING
        ]
        
        super().__init__(agent_id, "Escalation Agent AI", capabilities)
        
        # Core components
        self.escalation_config = escalation_config or {}
        
        # Escalation criteria and rules
        self.escalation_criteria = {
            "time_based": {
                "high_priority_hours": 4,
                "medium_priority_hours": 8,
                "low_priority_hours": 24
            },
            "complexity_based": {
                "multiple_failed_attempts": 3,
                "requires_specialist": True,
                "customer_vip": True
            },
            "impact_based": {
                "affects_multiple_users": True,
                "business_critical": True,
                "security_related": True
            }
        }
        
        # Escalation paths and levels
        self.escalation_paths = {
            "technical": {
                "level_1": "senior_technician",
                "level_2": "technical_lead",
                "level_3": "engineering_manager"
            },
            "management": {
                "level_1": "team_lead",
                "level_2": "department_manager",
                "level_3": "director"
            },
            "customer": {
                "level_1": "account_manager",
                "level_2": "customer_success",
                "level_3": "executive_team"
            }
        }
        
        # Learning and adaptation
        self.escalation_history = []
        self.escalation_outcomes = {}
        self.learning_enabled = True
        self.adaptation_threshold = 0.7  # Success rate threshold for rule adaptation
        
        # Performance tracking
        self.escalations_processed = 0
        self.successful_escalations = 0
        self.prevented_escalations = 0
        self.escalation_accuracy = 0.0
        
        # Add initial goals
        self.add_goal(
            description="Monitor tickets for escalation needs",
            priority=8,
            success_criteria=["95% escalation accuracy", "< 10% false escalations"]
        )
        
        self.add_goal(
            description="Optimize escalation paths and timing",
            priority=7,
            success_criteria=["Reduced escalation time", "Improved resolution rates"]
        )
        
        logger.info(f"Escalation Agent AI {self.agent_id} initialized with autonomous capabilities")
    
    async def think(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Core thinking process for escalation analysis.
        
        Args:
            context: Current context including ticket data
            
        Returns:
            Analysis results and escalation recommendations
        """
        self.state = AgentState.THINKING
        
        ticket_data = context.get('ticket_data', {})
        escalation_type = context.get('escalation_type', 'evaluation')
        
        # Analyze escalation need
        escalation_analysis = await self._analyze_escalation_need(ticket_data)
        
        # Determine escalation path
        path_analysis = await self._determine_escalation_path(ticket_data, escalation_analysis)
        
        # Assess escalation urgency
        urgency_assessment = await self._assess_escalation_urgency(ticket_data, escalation_analysis)
        
        # Generate escalation strategy
        escalation_strategy = await self._generate_escalation_strategy(
            escalation_analysis, path_analysis, urgency_assessment
        )
        
        thought_results = {
            "escalation_analysis": escalation_analysis,
            "path_analysis": path_analysis,
            "urgency_assessment": urgency_assessment,
            "escalation_strategy": escalation_strategy,
            "reasoning": f"Analyzed ticket {ticket_data.get('ticket_number', 'unknown')} "
                        f"for escalation: {escalation_analysis.get('needs_escalation', False)}"
        }
        
        logger.info(f"Escalation Agent thinking complete: {thought_results['reasoning']}")
        return thought_results
    
    async def plan(self, goal: AgentGoal, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Create a plan to achieve escalation goals.
        
        Args:
            goal: The goal to plan for
            context: Current context
            
        Returns:
            List of planned actions
        """
        self.state = AgentState.PLANNING
        
        ticket_data = context.get('ticket_data', {})
        thought_results = context.get('thought_results', {})
        
        plan = []
        
        if goal.description == "Monitor tickets for escalation needs":
            # Plan for escalation monitoring
            plan = [
                {
                    "action": "evaluate_escalation_criteria",
                    "description": "Evaluate if ticket meets escalation criteria",
                    "priority": 10,
                    "estimated_duration": 5
                },
                {
                    "action": "analyze_ticket_history",
                    "description": "Analyze ticket history and patterns",
                    "priority": 8,
                    "estimated_duration": 7
                },
                {
                    "action": "assess_current_assignment",
                    "description": "Assess current assignment effectiveness",
                    "priority": 7,
                    "estimated_duration": 4
                }
            ]
        
        elif goal.description == "Optimize escalation paths and timing":
            # Plan for escalation optimization
            escalation_needed = thought_results.get('escalation_analysis', {}).get('needs_escalation', False)
            
            if escalation_needed:
                plan = [
                    {
                        "action": "determine_escalation_path",
                        "description": "Determine optimal escalation path",
                        "priority": 10,
                        "estimated_duration": 6
                    },
                    {
                        "action": "prepare_escalation",
                        "description": "Prepare escalation documentation",
                        "priority": 9,
                        "estimated_duration": 8
                    },
                    {
                        "action": "execute_escalation",
                        "description": "Execute escalation process",
                        "priority": 10,
                        "estimated_duration": 5
                    },
                    {
                        "action": "notify_stakeholders",
                        "description": "Notify relevant stakeholders",
                        "priority": 8,
                        "estimated_duration": 3
                    }
                ]
            else:
                plan = [
                    {
                        "action": "monitor_progress",
                        "description": "Continue monitoring ticket progress",
                        "priority": 6,
                        "estimated_duration": 2
                    }
                ]
        
        logger.info(f"Escalation Agent planned {len(plan)} actions for goal: {goal.description}")
        return plan
    
    async def execute_action(self, action: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a specific action autonomously.
        
        Args:
            action: Action to execute
            context: Current context
            
        Returns:
            Execution results
        """
        self.state = AgentState.EXECUTING
        action_type = action.get('type', action.get('action', 'unknown'))
        
        try:
            if action_type == "evaluate_escalation_criteria":
                return await self._execute_evaluate_escalation_criteria(context)
            elif action_type == "determine_escalation_path":
                return await self._execute_determine_escalation_path(context)
            elif action_type == "prepare_escalation":
                return await self._execute_prepare_escalation(context)
            elif action_type == "execute_escalation":
                return await self._execute_escalation(context)
            elif action_type == "notify_stakeholders":
                return await self._execute_notify_stakeholders(context)
            elif action_type == "escalate_ticket":
                # Main escalation action
                return await self._execute_escalate_ticket(context)
            else:
                logger.warning(f"Unknown action type: {action_type}")
                return {"success": False, "error": f"Unknown action: {action_type}"}
                
        except Exception as e:
            logger.error(f"Error executing action {action_type}: {e}")
            return {"success": False, "error": str(e)}
        finally:
            self.state = AgentState.IDLE

    async def _execute_escalate_ticket(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the main ticket escalation workflow"""
        escalation_request = context.get('input_data', {}).get('escalation_request',
                                                              context.get('escalation_request', {}))
        ticket_data = escalation_request.get('ticket_data', {})

        try:
            # Step 1: Think about escalation needs
            thought_results = await self.think({
                "ticket_data": ticket_data,
                "escalation_type": escalation_request.get("type", "evaluation")
            })

            # Step 2: Evaluate escalation criteria
            criteria_result = await self._execute_evaluate_escalation_criteria({
                "ticket_data": ticket_data,
                "analysis": thought_results.get("escalation_analysis", {})
            })

            escalation_needed = criteria_result.get("escalation_needed", False)

            if escalation_needed:
                # Step 3: Determine escalation path
                path_result = await self._execute_determine_escalation_path({
                    "ticket_data": ticket_data,
                    "path_analysis": thought_results.get("path_analysis", {})
                })

                # Step 4: Prepare escalation
                preparation_result = await self._execute_prepare_escalation({
                    "ticket_data": ticket_data,
                    "escalation_path": path_result.get("escalation_path", {}),
                    "strategy": thought_results.get("escalation_strategy", {})
                })

                # Step 5: Execute escalation
                execution_result = await self._execute_escalation({
                    "ticket_data": ticket_data,
                    "escalation_details": preparation_result.get("escalation_details", {}),
                    "escalation_path": path_result.get("escalation_path", {})
                })

                # Step 6: Notify stakeholders
                notification_result = await self._execute_notify_stakeholders({
                    "ticket_data": ticket_data,
                    "escalation_result": execution_result,
                    "stakeholders": preparation_result.get("stakeholders", [])
                })

                # Update performance metrics
                self.escalations_processed += 1
                if execution_result.get("success", False):
                    self.successful_escalations += 1

                result = {
                    "success": True,
                    "escalation_executed": True,
                    "escalation_id": execution_result.get("escalation_id", ""),
                    "escalation_path": path_result.get("escalation_path", {}),
                    "escalation_level": path_result.get("escalation_level", ""),
                    "assigned_to": execution_result.get("assigned_to", ""),
                    "reasoning": thought_results.get("reasoning", ""),
                    "notifications_sent": notification_result.get("notifications_sent", 0)
                }
            else:
                # No escalation needed
                self.prevented_escalations += 1
                result = {
                    "success": True,
                    "escalation_executed": False,
                    "reason": criteria_result.get("reason", "Escalation criteria not met"),
                    "continue_monitoring": True,
                    "reasoning": thought_results.get("reasoning", "")
                }

            # Store escalation history
            self._store_escalation_history(ticket_data, result)

            return result

        except Exception as e:
            logger.error(f"Error in ticket escalation: {e}")
            return {"success": False, "error": str(e)}

    async def _analyze_escalation_need(self, ticket_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze if ticket needs escalation"""
        analysis = {
            "needs_escalation": False,
            "escalation_reasons": [],
            "escalation_score": 0.0,
            "criteria_met": []
        }

        # Time-based criteria
        created_time = ticket_data.get('created_at', '')
        if created_time:
            try:
                created_dt = datetime.fromisoformat(created_time.replace('Z', '+00:00'))
                hours_open = (datetime.now(timezone.utc) - created_dt).total_seconds() / 3600

                priority = ticket_data.get('classification', {}).get('PRIORITY', {}).get('Label', 'Medium')
                time_threshold = self.escalation_criteria["time_based"].get(f"{priority.lower()}_priority_hours", 8)

                if hours_open > time_threshold:
                    analysis["escalation_reasons"].append(f"Ticket open for {hours_open:.1f} hours (threshold: {time_threshold})")
                    analysis["escalation_score"] += 0.4
                    analysis["criteria_met"].append("time_based")
            except Exception as e:
                logger.warning(f"Error parsing created time: {e}")

        # Complexity-based criteria
        failed_attempts = ticket_data.get('failed_resolution_attempts', 0)
        if failed_attempts >= self.escalation_criteria["complexity_based"]["multiple_failed_attempts"]:
            analysis["escalation_reasons"].append(f"Multiple failed attempts: {failed_attempts}")
            analysis["escalation_score"] += 0.3
            analysis["criteria_met"].append("complexity_based")

        # Customer VIP status
        customer_type = ticket_data.get('customer_type', '').lower()
        if customer_type in ['vip', 'premium', 'enterprise']:
            analysis["escalation_reasons"].append("VIP customer")
            analysis["escalation_score"] += 0.2
            analysis["criteria_met"].append("vip_customer")

        # Impact-based criteria
        if ticket_data.get('affects_multiple_users', False):
            analysis["escalation_reasons"].append("Affects multiple users")
            analysis["escalation_score"] += 0.3
            analysis["criteria_met"].append("impact_based")

        if ticket_data.get('business_critical', False):
            analysis["escalation_reasons"].append("Business critical issue")
            analysis["escalation_score"] += 0.4
            analysis["criteria_met"].append("business_critical")

        # Determine if escalation is needed
        analysis["needs_escalation"] = analysis["escalation_score"] >= 0.5

        return analysis

    async def _determine_escalation_path(self, ticket_data: Dict[str, Any],
                                       escalation_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Determine the appropriate escalation path"""
        # Determine escalation type based on criteria
        criteria_met = escalation_analysis.get("criteria_met", [])

        if "business_critical" in criteria_met or "impact_based" in criteria_met:
            escalation_type = "management"
        elif "complexity_based" in criteria_met:
            escalation_type = "technical"
        elif "vip_customer" in criteria_met:
            escalation_type = "customer"
        else:
            escalation_type = "technical"  # Default

        # Determine escalation level
        escalation_score = escalation_analysis.get("escalation_score", 0.0)
        if escalation_score >= 0.8:
            escalation_level = "level_3"
        elif escalation_score >= 0.6:
            escalation_level = "level_2"
        else:
            escalation_level = "level_1"

        escalation_path = self.escalation_paths.get(escalation_type, {})
        escalation_target = escalation_path.get(escalation_level, "senior_technician")

        return {
            "escalation_type": escalation_type,
            "escalation_level": escalation_level,
            "escalation_target": escalation_target,
            "escalation_path": escalation_path,
            "escalation_score": escalation_score
        }

    async def _assess_escalation_urgency(self, ticket_data: Dict[str, Any],
                                       escalation_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Assess the urgency of escalation"""
        urgency_factors = []
        urgency_score = 0.0

        # Priority-based urgency
        priority = ticket_data.get('classification', {}).get('PRIORITY', {}).get('Label', 'Medium')
        if priority == 'Critical':
            urgency_score += 0.4
            urgency_factors.append("Critical priority")
        elif priority == 'High':
            urgency_score += 0.3
            urgency_factors.append("High priority")

        # Time-based urgency
        escalation_score = escalation_analysis.get("escalation_score", 0.0)
        if escalation_score >= 0.8:
            urgency_score += 0.3
            urgency_factors.append("High escalation score")

        # Customer impact urgency
        if ticket_data.get('affects_multiple_users', False):
            urgency_score += 0.2
            urgency_factors.append("Multiple users affected")

        if ticket_data.get('business_critical', False):
            urgency_score += 0.3
            urgency_factors.append("Business critical")

        # Determine urgency level
        if urgency_score >= 0.7:
            urgency_level = "immediate"
        elif urgency_score >= 0.5:
            urgency_level = "high"
        elif urgency_score >= 0.3:
            urgency_level = "medium"
        else:
            urgency_level = "low"

        return {
            "urgency_level": urgency_level,
            "urgency_score": urgency_score,
            "urgency_factors": urgency_factors,
            "estimated_response_time": self._get_response_time_for_urgency(urgency_level)
        }

    def _get_response_time_for_urgency(self, urgency_level: str) -> str:
        """Get expected response time for urgency level"""
        response_times = {
            "immediate": "15 minutes",
            "high": "1 hour",
            "medium": "4 hours",
            "low": "24 hours"
        }
        return response_times.get(urgency_level, "4 hours")

    async def _generate_escalation_strategy(self, escalation_analysis: Dict[str, Any],
                                          path_analysis: Dict[str, Any],
                                          urgency_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Generate escalation strategy"""
        strategy = {
            "approach": "standard",
            "communication_style": "professional",
            "documentation_level": "standard",
            "follow_up_required": True
        }

        urgency_level = urgency_assessment.get("urgency_level", "medium")
        escalation_type = path_analysis.get("escalation_type", "technical")

        # Adjust strategy based on urgency
        if urgency_level == "immediate":
            strategy["approach"] = "urgent"
            strategy["communication_style"] = "direct"
            strategy["documentation_level"] = "detailed"
        elif urgency_level == "high":
            strategy["approach"] = "expedited"
            strategy["documentation_level"] = "detailed"

        # Adjust strategy based on escalation type
        if escalation_type == "management":
            strategy["communication_style"] = "executive"
            strategy["documentation_level"] = "executive_summary"
        elif escalation_type == "customer":
            strategy["communication_style"] = "customer_focused"
            strategy["follow_up_required"] = True

        return strategy

    async def _execute_evaluate_escalation_criteria(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate if ticket meets escalation criteria"""
        try:
            ticket_data = context.get('ticket_data', {})
            analysis = context.get('analysis', {})

            # Use existing analysis or perform new analysis
            if not analysis:
                analysis = await self._analyze_escalation_need(ticket_data)

            escalation_needed = analysis.get("needs_escalation", False)
            escalation_score = analysis.get("escalation_score", 0.0)

            return {
                "success": True,
                "escalation_needed": escalation_needed,
                "escalation_score": escalation_score,
                "criteria_met": analysis.get("criteria_met", []),
                "reasons": analysis.get("escalation_reasons", []),
                "reason": "Escalation criteria met" if escalation_needed else "Escalation criteria not met"
            }
        except Exception as e:
            logger.error(f"Escalation criteria evaluation failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "escalation_needed": False
            }

    async def _execute_determine_escalation_path(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Determine optimal escalation path"""
        try:
            ticket_data = context.get('ticket_data', {})
            path_analysis = context.get('path_analysis', {})

            # Use existing analysis or perform new analysis
            if not path_analysis:
                escalation_analysis = await self._analyze_escalation_need(ticket_data)
                path_analysis = await self._determine_escalation_path(ticket_data, escalation_analysis)

            return {
                "success": True,
                "escalation_path": path_analysis,
                "escalation_type": path_analysis.get("escalation_type", "technical"),
                "escalation_level": path_analysis.get("escalation_level", "level_1"),
                "escalation_target": path_analysis.get("escalation_target", "senior_technician")
            }
        except Exception as e:
            logger.error(f"Escalation path determination failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _execute_prepare_escalation(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare escalation documentation and details"""
        try:
            ticket_data = context.get('ticket_data', {})
            escalation_path = context.get('escalation_path', {})
            strategy = context.get('strategy', {})

            # Prepare escalation documentation
            escalation_details = {
                "escalation_id": f"esc_{uuid.uuid4().hex[:8]}",
                "ticket_number": ticket_data.get('ticket_number', 'Unknown'),
                "escalation_timestamp": datetime.now(timezone.utc).isoformat(),
                "escalation_reason": "Automated escalation based on criteria",
                "escalation_type": escalation_path.get("escalation_type", "technical"),
                "escalation_level": escalation_path.get("escalation_level", "level_1"),
                "target_assignee": escalation_path.get("escalation_target", "senior_technician"),
                "original_assignee": ticket_data.get('assigned_technician', 'Unknown'),
                "customer_impact": self._assess_customer_impact(ticket_data),
                "business_impact": self._assess_business_impact(ticket_data),
                "recommended_actions": self._generate_recommended_actions(ticket_data),
                "escalation_summary": self._generate_escalation_summary(ticket_data, escalation_path)
            }

            # Identify stakeholders to notify
            stakeholders = self._identify_stakeholders(ticket_data, escalation_path)

            return {
                "success": True,
                "escalation_details": escalation_details,
                "stakeholders": stakeholders,
                "documentation_prepared": True
            }
        except Exception as e:
            logger.error(f"Escalation preparation failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _execute_escalation(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the actual escalation"""
        try:
            ticket_data = context.get('ticket_data', {})
            escalation_details = context.get('escalation_details', {})
            escalation_path = context.get('escalation_path', {})

            escalation_id = escalation_details.get('escalation_id', f"esc_{uuid.uuid4().hex[:8]}")
            target_assignee = escalation_details.get('target_assignee', 'senior_technician')

            # Simulate escalation execution (would integrate with actual ticketing system)
            logger.info(f"Executing escalation {escalation_id} for ticket {ticket_data.get('ticket_number', 'Unknown')}")
            logger.info(f"Escalating to: {target_assignee}")

            # Update ticket assignment (simulation)
            updated_ticket = dict(ticket_data)
            updated_ticket['assigned_technician'] = target_assignee
            updated_ticket['escalation_level'] = escalation_path.get('escalation_level', 'level_1')
            updated_ticket['escalated_at'] = datetime.now(timezone.utc).isoformat()
            updated_ticket['escalation_id'] = escalation_id

            return {
                "success": True,
                "escalation_id": escalation_id,
                "assigned_to": target_assignee,
                "escalation_level": escalation_path.get('escalation_level', 'level_1'),
                "escalation_timestamp": datetime.now(timezone.utc).isoformat(),
                "updated_ticket": updated_ticket
            }
        except Exception as e:
            logger.error(f"Escalation execution failed: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def _execute_notify_stakeholders(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Notify relevant stakeholders about escalation"""
        try:
            ticket_data = context.get('ticket_data', {})
            escalation_result = context.get('escalation_result', {})
            stakeholders = context.get('stakeholders', [])

            notifications_sent = 0

            for stakeholder in stakeholders:
                # Simulate notification sending
                logger.info(f"Notifying {stakeholder.get('name', 'Unknown')} about escalation {escalation_result.get('escalation_id', 'Unknown')}")
                notifications_sent += 1

            return {
                "success": True,
                "notifications_sent": notifications_sent,
                "stakeholders_notified": len(stakeholders)
            }
        except Exception as e:
            logger.error(f"Stakeholder notification failed: {e}")
            return {
                "success": False,
                "error": str(e),
                "notifications_sent": 0
            }

    def _assess_customer_impact(self, ticket_data: Dict[str, Any]) -> str:
        """Assess customer impact level"""
        if ticket_data.get('affects_multiple_users', False):
            return "High - Multiple users affected"
        elif ticket_data.get('customer_type', '').lower() in ['vip', 'premium']:
            return "Medium - VIP customer affected"
        else:
            return "Low - Single user affected"

    def _assess_business_impact(self, ticket_data: Dict[str, Any]) -> str:
        """Assess business impact level"""
        if ticket_data.get('business_critical', False):
            return "High - Business critical system affected"
        elif ticket_data.get('affects_multiple_users', False):
            return "Medium - Productivity impact"
        else:
            return "Low - Minimal business impact"

    def _generate_recommended_actions(self, ticket_data: Dict[str, Any]) -> List[str]:
        """Generate recommended actions for escalation"""
        actions = []

        priority = ticket_data.get('classification', {}).get('PRIORITY', {}).get('Label', 'Medium')
        if priority in ['Critical', 'High']:
            actions.append("Immediate attention required")
            actions.append("Consider emergency response procedures")

        if ticket_data.get('failed_resolution_attempts', 0) > 2:
            actions.append("Review previous resolution attempts")
            actions.append("Consider alternative approaches")

        if ticket_data.get('business_critical', False):
            actions.append("Engage business stakeholders")
            actions.append("Prepare status updates for management")

        return actions or ["Standard escalation procedures"]

    def _generate_escalation_summary(self, ticket_data: Dict[str, Any],
                                   escalation_path: Dict[str, Any]) -> str:
        """Generate escalation summary"""
        ticket_number = ticket_data.get('ticket_number', 'Unknown')
        title = ticket_data.get('title', 'No title')
        escalation_type = escalation_path.get('escalation_type', 'technical')
        escalation_level = escalation_path.get('escalation_level', 'level_1')

        summary = f"""
        Escalation Summary for Ticket {ticket_number}

        Title: {title}
        Escalation Type: {escalation_type.title()}
        Escalation Level: {escalation_level.replace('_', ' ').title()}

        Customer Impact: {self._assess_customer_impact(ticket_data)}
        Business Impact: {self._assess_business_impact(ticket_data)}

        Recommended Actions:
        {chr(10).join(f"- {action}" for action in self._generate_recommended_actions(ticket_data))}
        """

        return summary.strip()

    def _identify_stakeholders(self, ticket_data: Dict[str, Any],
                             escalation_path: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify stakeholders to notify about escalation"""
        stakeholders = []

        # Add original assignee
        original_assignee = ticket_data.get('assigned_technician', '')
        if original_assignee:
            stakeholders.append({
                "name": original_assignee,
                "role": "original_assignee",
                "notification_type": "escalation_notice"
            })

        # Add new assignee
        new_assignee = escalation_path.get('escalation_target', '')
        if new_assignee:
            stakeholders.append({
                "name": new_assignee,
                "role": "new_assignee",
                "notification_type": "escalation_assignment"
            })

        # Add customer if VIP or high impact
        customer_type = ticket_data.get('customer_type', '').lower()
        if customer_type in ['vip', 'premium'] or ticket_data.get('business_critical', False):
            customer_name = ticket_data.get('customer_name', '')
            if customer_name:
                stakeholders.append({
                    "name": customer_name,
                    "role": "customer",
                    "notification_type": "escalation_update"
                })

        # Add management for high-level escalations
        escalation_level = escalation_path.get('escalation_level', 'level_1')
        if escalation_level in ['level_2', 'level_3']:
            stakeholders.append({
                "name": "Management Team",
                "role": "management",
                "notification_type": "escalation_alert"
            })

        return stakeholders

    def _store_escalation_history(self, ticket_data: Dict[str, Any],
                                escalation_result: Dict[str, Any]) -> None:
        """Store escalation history for learning"""
        history_entry = {
            "ticket_number": ticket_data.get('ticket_number', 'Unknown'),
            "escalation_timestamp": datetime.now(timezone.utc).isoformat(),
            "escalation_executed": escalation_result.get('escalation_executed', False),
            "escalation_id": escalation_result.get('escalation_id', ''),
            "escalation_path": escalation_result.get('escalation_path', {}),
            "success": escalation_result.get('success', False),
            "reasoning": escalation_result.get('reasoning', '')
        }

        self.escalation_history.append(history_entry)

        # Keep only last 1000 entries to manage memory
        if len(self.escalation_history) > 1000:
            self.escalation_history = self.escalation_history[-1000:]

    async def escalate_ticket_autonomous(self, escalation_request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Main autonomous escalation method.
        This is the entry point for autonomous ticket escalation.
        """
        # Add escalation goal
        goal_id = self.add_goal(
            description=f"Escalate ticket: {escalation_request.get('ticket_data', {}).get('ticket_number', 'Unknown')}",
            priority=8,
            context={"escalation_request": escalation_request}
        )

        try:
            # Execute the main escalation action
            result = await self.execute_action(
                action={"type": "escalate_ticket"},
                context={"input_data": {"escalation_request": escalation_request}}
            )

            # Learn from the outcome
            from .base_agent import AgentDecision  # Ensure AgentDecision is imported

            dummy_decision = AgentDecision(
                action="escalate_ticket",
                context={"escalation_request": escalation_request},
                result=result
            )
            await self.learn_from_outcome(
                decision=dummy_decision,
                outcome=result
            )

            # Complete the goal
            self.complete_goal(goal_id, "completed" if result.get("success") else "failed")

            return result

        except Exception as e:
            logger.error(f"Autonomous escalation failed: {e}")
            self.complete_goal(goal_id, "failed")
            return {"success": False, "error": str(e)}

    def get_agent_metrics(self) -> Dict[str, Any]:
        """Get agent-specific performance metrics"""
        base_metrics = self.get_status()

        # Add escalation-specific metrics
        escalation_metrics = {
            "escalations_processed": self.escalations_processed,
            "successful_escalations": self.successful_escalations,
            "prevented_escalations": self.prevented_escalations,
            "escalation_success_rate": (
                self.successful_escalations / max(self.escalations_processed, 1)
            ),
            "escalation_accuracy": self.escalation_accuracy,
            "escalation_history_count": len(self.escalation_history),
            "learning_enabled": self.learning_enabled,
            "adaptation_threshold": self.adaptation_threshold,
            "escalation_criteria": dict(self.escalation_criteria),
            "escalation_paths": dict(self.escalation_paths)
        }

        return {**base_metrics, **escalation_metrics}
